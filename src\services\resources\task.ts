import { ProjectTaskResponse } from "@/types/task";
import { axiosClient, getAccessToken } from "../api";

export const getTasksByMilestoneId = async (milestoneId: string) => {
  const accessToken = getAccessToken();
  return await axiosClient.get<ProjectTaskResponse[]>(
    `/task/filter?MilestoneId=${milestoneId}`,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  );
};
